import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:noeji/services/firebase/cloud_function_service.dart';
import 'package:noeji/services/llm/llm_service.dart';
import 'package:noeji/services/remote_config/remote_config_providers.dart';

class MockCloudFunctionService extends Mock implements CloudFunctionService {}

class MockLlmPrompts extends Mock implements LlmPrompts {}

class MockLlmModelConfig extends Mock implements LlmModelConfig {}

void main() {
  group('GeminiService', () {
    late GeminiService geminiService;
    late MockCloudFunctionService mockCloudFunctionService;
    late MockLlmPrompts mockLlmPrompts;
    late MockLlmModelConfig mockLlmModelConfig;

    setUp(() {
      mockCloudFunctionService = MockCloudFunctionService();
      mockLlmPrompts = MockLlmPrompts();
      mockLlmModelConfig = MockLlmModelConfig();

      // Set up mock responses for the model config
      when(
        mockLlmModelConfig.getAudioTranscriptionModel(
          userTier: anyNamed('userTier'),
        ),
      ).thenReturn('test-model');
      when(
        mockLlmModelConfig.getAudioTranscriptionConfig(
          userTier: anyNamed('userTier'),
        ),
      ).thenReturn({'temperature': 0.1});

      geminiService = GeminiService(
        cloudFunctionService: mockCloudFunctionService,
        llmPrompts: mockLlmPrompts,
        llmModelConfig: mockLlmModelConfig,
      );
    });

    test(
      'transcribeAudio returns failure when file does not exist for new ideabook',
      () async {
        // Arrange
        const nonExistentFilePath = 'non_existent_file.aac';

        // Act
        final result = await geminiService.transcribeAudio(
          nonExistentFilePath,
          useCase: TranscriptionUseCase.newIdeabook,
        );

        // Assert
        expect(result.isSuccess, isFalse);
        expect(result.errorMessage, 'Audio file does not exist');
      },
    );

    test(
      'transcribeAudio returns failure when file does not exist for new idea',
      () async {
        // Arrange
        const nonExistentFilePath = 'non_existent_file.aac';

        // Act
        final result = await geminiService.transcribeAudio(
          nonExistentFilePath,
          useCase: TranscriptionUseCase.newIdea,
        );

        // Assert
        expect(result.isSuccess, isFalse);
        expect(result.errorMessage, 'Audio file does not exist');
      },
    );

    // Skip HTTP client tests for now
    // In a real implementation, we would modify the service to accept a client for testing
  });
}
